import 'dart:async';
import 'dart:typed_data';
import 'dart:collection';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:file_selector/file_selector.dart';
import '../models/upload_file_model.dart';
import '../core/services/firebase_service.dart';
import '../core/utils/anr_prevention.dart';
import 'cloud_functions_service.dart';
import 'firebase_storage_category_service.dart';

/// Optimized file upload service using Cloud Functions for heavy processing
class OptimizedFileUploadService {
  final FirebaseService _firebaseService = FirebaseService.instance;
  final CloudFunctionsService _cloudFunctions = CloudFunctionsService.instance;
  final FirebaseStorageCategoryService _categoryStorageService =
      FirebaseStorageCategoryService();
  final Map<String, StreamSubscription> _uploadSubscriptions = {};
  final Map<String, UploadTask?> _uploadTasks = {};

  /// Check Firebase connectivity before upload
  Future<bool> checkConnectivity() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return false;

      // Quick connectivity test
      final documentsRef = _firebaseService.storage.ref().child('documents');
      await documentsRef.listAll().timeout(const Duration(seconds: 5));
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Validate file using Cloud Functions before upload
  Future<bool> validateFile(UploadFileModel file) async {
    try {
      debugPrint('🔍 Validating file via Cloud Functions: ${file.fileName}');

      final fileSize = await file.file.length();
      final result = await _cloudFunctions.validateFile(
        fileName: file.fileName,
        fileSize: fileSize,
        contentType: _getContentType(file.fileName, file.fileType),
      );

      return result['isValid'] == true;
    } catch (e) {
      debugPrint('❌ File validation failed: $e');
      return false;
    }
  }

  /// Upload file with Cloud Functions processing
  Future<String> uploadFile(
    UploadFileModel file, {
    required Function(double) onProgress,
  }) async {
    try {
      // Get current user and ensure authentication
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('🚀 Starting optimized file upload: ${file.fileName}');

      // Step 1: Validate file using Cloud Functions
      final isValid = await validateFile(file);
      if (!isValid) {
        throw Exception('File validation failed');
      }

      // Step 2: Read file with chunking to prevent ANR
      final fileSize = await file.file.length();
      final bytes = await _readFileWithChunking(file.file, fileSize, (
        progress,
      ) {
        onProgress(progress * 0.3); // 30% for reading
      });

      if (bytes == null) {
        throw Exception('Failed to read file - operation timed out');
      }

      // Step 3: Upload to Firebase Storage (lightweight)
      final storagePath = _getStoragePath(file);
      final storageRef = _firebaseService.storage.ref().child(storagePath);

      final uploadTask = storageRef.putData(
        bytes,
        SettableMetadata(
          contentType: _getContentType(file.fileName, file.fileType),
          customMetadata: {
            'originalName': file.fileName,
            'uploadedBy': currentUser.uid,
            'fileSize': bytes.length.toString(),
            'categoryId': file.categoryId ?? 'uncategorized',
            'uploadTimestamp': DateTime.now().millisecondsSinceEpoch.toString(),
          },
        ),
      );

      // Store upload task for control
      _uploadTasks[file.id] = uploadTask;

      // Listen to upload progress
      final subscription = uploadTask.snapshotEvents.listen((
        TaskSnapshot snapshot,
      ) {
        if (snapshot.state == TaskState.running) {
          final progress = (snapshot.bytesTransferred / snapshot.totalBytes);
          onProgress(30 + (progress * 40)); // 30-70% for upload
        }
      }, onError: (error) => _handleUploadError(error));

      _uploadSubscriptions[file.id] = subscription;

      // Wait for upload to complete
      final snapshot = await uploadTask.timeout(
        const Duration(minutes: 2), // Reduced timeout
        onTimeout: () {
          uploadTask.cancel();
          throw Exception('Upload timeout');
        },
      );

      onProgress(70); // Upload complete

      // Step 4: Process file using Cloud Functions (heavy operations)
      debugPrint('📋 Processing file metadata via Cloud Functions...');

      final result = await _cloudFunctions.processFileUpload(
        filePath: snapshot.ref.fullPath,
        contentType: _getContentType(file.fileName, file.fileType),
        metadata: {
          'originalName': file.fileName,
          'uploadedBy': currentUser.uid,
          'fileSize': bytes.length,
          'categoryId': file.categoryId,
        },
        categoryId: file.categoryId,
      );

      onProgress(100); // Complete

      // Clean up
      _cleanup(file.id);

      debugPrint('✅ File upload and processing completed successfully');
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      _cleanup(file.id);
      debugPrint('❌ Optimized file upload failed: $e');
      rethrow;
    }
  }

  /// Upload multiple files concurrently with Cloud Functions
  Future<List<String>> uploadMultipleFiles(
    List<UploadFileModel> files, {
    required Function(String fileId, double progress) onProgress,
    int maxConcurrency = 3,
  }) async {
    debugPrint('🚀 Starting batch upload of ${files.length} files');

    final results = <String>[];
    final semaphore = Semaphore(maxConcurrency);

    final futures = files.map((file) async {
      await semaphore.acquire();
      try {
        final downloadUrl = await uploadFile(
          file,
          onProgress: (progress) => onProgress(file.id, progress),
        );
        results.add(downloadUrl);
        return downloadUrl;
      } finally {
        semaphore.release();
      }
    });

    await Future.wait(futures);
    debugPrint('✅ Batch upload completed: ${results.length} files');
    return results;
  }

  /// Generate thumbnail using Cloud Functions
  Future<String?> generateThumbnail(String filePath) async {
    try {
      return await _cloudFunctions.generateThumbnail(filePath);
    } catch (e) {
      debugPrint('❌ Thumbnail generation failed: $e');
      return null;
    }
  }

  // Helper methods

  String _getStoragePath(UploadFileModel file) {
    final sanitizedFileName = _sanitizeFileName(file.fileName);

    if (file.categoryId != null && file.categoryId != 'uncategorized') {
      return _categoryStorageService.getUploadPath(
        file.categoryId!,
        'category',
        sanitizedFileName,
      );
    } else {
      return _categoryStorageService.getUncategorizedUploadPath(
        sanitizedFileName,
      );
    }
  }

  String _sanitizeFileName(String fileName) {
    return fileName
        .replaceAll(RegExp(r'[^\w\s\-\.]'), '_')
        .replaceAll(RegExp(r'\s+'), '_')
        .toLowerCase();
  }

  String _getContentType(String fileName, String fallbackType) {
    final extension = fileName.split('.').last.toLowerCase();

    const contentTypes = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx':
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'xls': 'application/vnd.ms-excel',
      'xlsx':
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'ppt': 'application/vnd.ms-powerpoint',
      'pptx':
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'txt': 'text/plain',
    };

    return contentTypes[extension] ??
        (fallbackType.isNotEmpty ? fallbackType : 'application/octet-stream');
  }

  Future<Uint8List?> _readFileWithChunking(
    XFile file,
    int fileSize,
    Function(double) onProgress,
  ) async {
    try {
      // For small files, read normally
      if (fileSize < 5 * 1024 * 1024) {
        return await ANRPrevention.executeWithTimeout(
          file.readAsBytes(),
          timeout: const Duration(seconds: 10),
          operationName: 'Small File Reading',
        );
      }

      // For large files, use streaming
      final stream = file.openRead();
      final chunks = <List<int>>[];
      int totalRead = 0;

      await for (final chunk in stream) {
        chunks.add(chunk);
        totalRead += chunk.length;
        onProgress(totalRead / fileSize);

        // Prevent UI blocking
        if (chunks.length % 5 == 0) {
          await Future.delayed(const Duration(milliseconds: 10));
        }

        if (totalRead >= fileSize) break;
      }

      // Combine chunks
      final totalBytes = chunks.fold<int>(
        0,
        (sum, chunk) => sum + chunk.length,
      );
      final result = Uint8List(totalBytes);
      int offset = 0;

      for (final chunk in chunks) {
        result.setRange(offset, offset + chunk.length, chunk);
        offset += chunk.length;
      }

      return result;
    } catch (e) {
      debugPrint('❌ Chunked file reading failed: $e');
      return null;
    }
  }

  void _handleUploadError(dynamic error) {
    if (error is FirebaseException) {
      switch (error.code) {
        case 'cancelled':
          throw Exception('Upload cancelled by user');
        case 'unauthenticated':
          throw Exception('Authentication required - please login again');
        case 'unauthorized':
          throw Exception('Permission denied - check access rights');
        case 'retry-limit-exceeded':
          throw Exception('Network error - please try again');
        case 'quota-exceeded':
          throw Exception('Storage quota exceeded');
        default:
          throw Exception('Upload failed: ${error.message}');
      }
    } else {
      throw Exception('Upload failed: ${error.toString()}');
    }
  }

  // Upload control methods
  void pauseUpload(String fileId) => _uploadTasks[fileId]?.pause();
  void resumeUpload(String fileId) => _uploadTasks[fileId]?.resume();
  void cancelUpload(String fileId) {
    _uploadTasks[fileId]?.cancel();
    _cleanup(fileId);
  }

  void _cleanup(String fileId) {
    _uploadSubscriptions[fileId]?.cancel();
    _uploadSubscriptions.remove(fileId);
    _uploadTasks.remove(fileId);
  }

  Map<String, dynamic> getUploadStats() {
    return {
      'activeUploads': _uploadTasks.length,
      'totalSubscriptions': _uploadSubscriptions.length,
    };
  }

  void dispose() {
    for (final subscription in _uploadSubscriptions.values) {
      subscription.cancel();
    }
    _uploadSubscriptions.clear();

    for (final task in _uploadTasks.values) {
      task?.cancel();
    }
    _uploadTasks.clear();
  }
}

/// Simple semaphore for controlling concurrency
class Semaphore {
  final int maxCount;
  int _currentCount;
  final Queue<Completer<void>> _waitQueue = Queue<Completer<void>>();

  Semaphore(this.maxCount) : _currentCount = maxCount;

  Future<void> acquire() async {
    if (_currentCount > 0) {
      _currentCount--;
      return;
    }

    final completer = Completer<void>();
    _waitQueue.add(completer);
    return completer.future;
  }

  void release() {
    if (_waitQueue.isNotEmpty) {
      final completer = _waitQueue.removeFirst();
      completer.complete();
    } else {
      _currentCount++;
    }
  }
}
